# Sistem Machine Learning Tanpa Framework

Sistem Machine Learning web yang dibuat dari nol tanpa menggunakan framework web apapun. Frontend menggunakan HTML/CSS/JavaScript murni, backend menggunakan HTTP server built-in Python, dan model ML menggunakan scikit-learn.

## 🚀 Fitur

- **Frontend Tanpa Framework**: HTML, CSS, JavaScript murni
- **Backend Sederhana**: HTTP server built-in Python
- **Model ML**: Linear Regression, Random Forest, SVM
- **Upload Data**: Support upload CSV atau gunakan dataset sample
- **Real-time Training**: Training model dengan progress indicator
- **Prediksi**: Interface untuk melakukan prediksi
- **Metrics**: <PERSON><PERSON><PERSON> a<PERSON>, MSE, R² score
- **Responsive Design**: UI yang responsive dan modern

## 📋 Persyaratan

- Python 3.7+
- pip (untuk install dependencies)

## 🛠️ Instalasi

1. **Clone atau download project ini**

2. **Install dependencies Python:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Jalankan server:**
   ```bash
   python server.py
   ```

4. **Buka browser dan akses:**
   ```
   http://localhost:8000
   ```

## 📁 Struktur Project

```
ML/
├── index.html          # Frontend utama
├── style.css           # Styling CSS
├── script.js           # JavaScript untuk interaksi
├── server.py           # HTTP server backend
├── ml_model.py         # Handler model ML
├── data_handler.py     # Handler data processing
├── requirements.txt    # Dependencies Python
└── README.md          # Dokumentasi
```

## 🎯 Cara Penggunaan

### 1. Training Model

1. Pilih jenis model (Linear Regression, Random Forest, atau SVM)
2. Upload file CSV (opsional) atau gunakan dataset sample
3. Set test size (0.1 - 0.5)
4. Klik "Mulai Training"
5. Tunggu hingga training selesai

### 2. Melakukan Prediksi

1. Pastikan model sudah ditraining
2. Masukkan nilai untuk setiap feature
3. Klik "Prediksi"
4. Lihat hasil prediksi

### 3. Melihat Hasil

1. Tab "Hasil" menampilkan metrics model:
   - Akurasi
   - Mean Squared Error
   - R² Score
   - Jumlah training samples

## 📊 Format Data CSV

Jika ingin upload data sendiri, pastikan format CSV seperti ini:

```csv
feature_1,feature_2,feature_3,target
1.2,3.4,5.6,10.5
2.1,4.3,6.5,12.3
...
```

- Kolom terakhir akan dianggap sebagai target/label
- Kolom lainnya sebagai features
- Header wajib ada

## 🔧 Kustomisasi

### Menambah Model Baru

Edit file `ml_model.py` pada bagian `model_options`:

```python
self.model_options = {
    'linear_regression': LinearRegression(),
    'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
    'svm': SVR(kernel='rbf'),
    'model_baru': ModelBaru()  # Tambah di sini
}
```

### Mengubah Port Server

```bash
python server.py 8080  # Jalankan di port 8080
```

### Kustomisasi UI

Edit file `style.css` untuk mengubah tampilan atau `script.js` untuk mengubah behavior.

## 🐛 Troubleshooting

### Server tidak bisa diakses
- Pastikan port 8000 tidak digunakan aplikasi lain
- Coba jalankan dengan port berbeda: `python server.py 8080`

### Error saat training
- Pastikan format CSV benar
- Cek apakah ada missing values yang berlebihan
- Pastikan minimal 10 samples data

### Prediksi error
- Pastikan model sudah ditraining
- Cek apakah input sesuai dengan jumlah features

## 📝 Catatan Teknis

- **Tidak menggunakan framework web** seperti Flask, Django, FastAPI
- **HTTP server built-in Python** untuk handling requests
- **CORS enabled** untuk komunikasi frontend-backend
- **Model persistence** menggunakan pickle
- **Responsive design** tanpa CSS framework

## 🎨 Screenshot

Interface akan menampilkan:
- Tab Training untuk melatih model
- Tab Prediksi untuk melakukan prediksi
- Tab Hasil untuk melihat metrics
- Design modern dengan gradient background
- Loading indicators dan status updates

## 📄 Lisensi

Project ini dibuat untuk pembelajaran dan dapat digunakan secara bebas.

## 🤝 Kontribusi

Silakan buat issue atau pull request untuk perbaikan dan penambahan fitur.
