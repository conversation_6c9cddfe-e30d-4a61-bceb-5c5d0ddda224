<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem ML Tanpa Framework</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 Sistem Machine Learning</h1>
            <p>Sistem ML dari nol tanpa framework web</p>
        </header>

        <div class="tabs">
            <button class="tab-button active" onclick="showTab('training')">Training Model</button>
            <button class="tab-button" onclick="showTab('prediction')">Prediksi</button>
            <button class="tab-button" onclick="showTab('results')">Hasil</button>
        </div>

        <!-- Tab Training -->
        <div id="training" class="tab-content active">
            <div class="card">
                <h2>Training Model ML</h2>
                <form id="trainingForm">
                    <div class="form-group">
                        <label for="modelType"><PERSON><PERSON><PERSON> Model:</label>
                        <select id="modelType" name="modelType">
                            <option value="linear_regression">Linear Regression</option>
                            <option value="random_forest">Random Forest</option>
                            <option value="svm">Support Vector Machine</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="dataFile">Upload Data CSV (opsional):</label>
                        <input type="file" id="dataFile" name="dataFile" accept=".csv">
                        <small>Atau gunakan dataset default jika tidak upload</small>
                    </div>

                    <div class="form-group">
                        <label for="testSize">Test Size (0.1 - 0.5):</label>
                        <input type="number" id="testSize" name="testSize" min="0.1" max="0.5" step="0.1" value="0.2">
                    </div>

                    <button type="submit" class="btn btn-primary">🚀 Mulai Training</button>
                </form>

                <div id="trainingStatus" class="status-box" style="display: none;">
                    <h3>Status Training:</h3>
                    <div id="trainingProgress"></div>
                </div>
            </div>
        </div>

        <!-- Tab Prediction -->
        <div id="prediction" class="tab-content">
            <div class="card">
                <h2>Prediksi dengan Model</h2>
                <form id="predictionForm">
                    <div class="form-group">
                        <label>Input Data untuk Prediksi:</label>
                        <div id="inputFields">
                            <!-- Input fields akan di-generate secara dinamis -->
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">🔮 Prediksi</button>
                </form>

                <div id="predictionResult" class="result-box" style="display: none;">
                    <h3>Hasil Prediksi:</h3>
                    <div id="predictionOutput"></div>
                </div>
            </div>
        </div>

        <!-- Tab Results -->
        <div id="results" class="tab-content">
            <div class="card">
                <h2>Hasil dan Metrik Model</h2>
                <div id="modelMetrics" class="metrics-container">
                    <p>Belum ada model yang ditraining</p>
                </div>

                <div id="visualizations" class="viz-container">
                    <!-- Visualisasi akan ditampilkan di sini -->
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>© 2024 Sistem ML Tanpa Framework - Dibuat dari nol</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
