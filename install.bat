@echo off
echo ========================================
echo   Sistem ML Tanpa Framework - Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python tidak ditemukan!
    echo.
    echo 📥 Silakan install Python terlebih dahulu:
    echo    1. Download dari https://python.org/downloads/
    echo    2. Install dengan mencentang "Add Python to PATH"
    echo    3. Restart command prompt
    echo    4. Jalankan script ini lagi
    echo.
    pause
    exit /b 1
)

echo ✅ Python ditemukan
python --version

echo.
echo 📦 Installing dependencies...
python -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Gagal install dependencies
    pause
    exit /b 1
)

echo.
echo ✅ Setup selesai!
echo.
echo 🚀 Untuk menjalankan sistem:
echo    python server.py
echo.
echo 🌐 Kemudian buka browser ke:
echo    http://localhost:8000
echo.
pause
