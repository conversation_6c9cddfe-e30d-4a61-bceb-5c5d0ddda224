@echo off
echo ========================================
echo   Sistem ML Tanpa Framework - Setup
echo ========================================
echo.

REM Set Python path untuk lokasi custom
set "PYTHON_CUSTOM_PATH=C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13"
set "PYTHON_EXE="

REM Check if Python is in PATH
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set "PYTHON_EXE=python"
    echo ✅ Python ditemukan di PATH
    goto :python_found
)

REM Check if Python is in custom location
if exist "%PYTHON_CUSTOM_PATH%\python.exe" (
    set "PYTHON_EXE=%PYTHON_CUSTOM_PATH%\python.exe"
    echo ✅ Python ditemukan di lokasi custom
    goto :python_found
)

REM Try common Python locations
for %%p in (
    "C:\Python*\python.exe"
    "C:\Program Files\Python*\python.exe"
    "C:\Program Files (x86)\Python*\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python*\python.exe"
    "%APPDATA%\Local\Programs\Python\Python*\python.exe"
) do (
    if exist "%%p" (
        set "PYTHON_EXE=%%p"
        echo ✅ Python ditemukan di %%p
        goto :python_found
    )
)

echo ❌ Python tidak ditemukan!
echo.
echo 📍 Lokasi yang dicari:
echo    - PATH environment
echo    - %PYTHON_CUSTOM_PATH%
echo    - C:\Python*\
echo    - Program Files locations
echo    - Local AppData locations
echo.
echo 📥 Solusi:
echo    1. Download dari https://python.org/downloads/
echo    2. Install dengan mencentang "Add Python to PATH"
echo    3. Atau edit script ini dan set PYTHON_CUSTOM_PATH ke lokasi Python Anda
echo.
pause
exit /b 1

:python_found

"%PYTHON_EXE%" --version

echo.
echo 📦 Installing dependencies...
"%PYTHON_EXE%" -m pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Gagal install dependencies
    echo 💡 Coba manual: "%PYTHON_EXE%" -m pip install numpy pandas scikit-learn
    pause
    exit /b 1
)

echo.
echo ✅ Setup selesai!
echo.
echo 🚀 Untuk menjalankan sistem:
echo    "%PYTHON_EXE%" server.py
echo.
echo 🌐 Kemudian buka browser ke:
echo    http://localhost:8000
echo.
echo 📝 Python executable: %PYTHON_EXE%
echo.
pause
