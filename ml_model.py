"""
Machine Learning Model Handler
Menggunakan scikit-learn untuk implementasi model ML
"""

import numpy as np
import pandas as pd
import pickle
import os
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score
from sklearn.datasets import make_regression
import io

class MLModel:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.model_type = None
        self.feature_names = []
        self.is_trained = False
        self.metrics = {}
        
        # Model options
        self.model_options = {
            'linear_regression': LinearRegression(),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'svm': SVR(kernel='rbf')
        }

    def _generate_sample_data(self):
        """Generate sample dataset jika tidak ada data yang diupload"""
        print("Menggunakan dataset sample...")
        
        # Generate regression dataset
        X, y = make_regression(
            n_samples=1000,
            n_features=5,
            noise=0.1,
            random_state=42
        )
        
        # Create feature names
        feature_names = [f'feature_{i+1}' for i in range(X.shape[1])]
        
        # Convert to DataFrame
        df = pd.DataFrame(X, columns=feature_names)
        df['target'] = y
        
        return df, feature_names

    def _load_data(self, data_file=None):
        """Load data dari file atau generate sample data"""
        if data_file is not None:
            try:
                # Read CSV from uploaded file
                df = pd.read_csv(data_file)
                print(f"Data loaded: {df.shape}")
                
                # Assume last column is target
                feature_names = df.columns[:-1].tolist()
                
                return df, feature_names
                
            except Exception as e:
                print(f"Error loading data: {e}")
                return self._generate_sample_data()
        else:
            return self._generate_sample_data()

    def train(self, model_type='linear_regression', test_size=0.2, data_file=None):
        """Train the ML model"""
        try:
            # Load data
            df, feature_names = self._load_data(data_file)
            self.feature_names = feature_names
            
            # Prepare features and target
            X = df[feature_names].values
            y = df.iloc[:, -1].values  # Last column as target
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Select and train model
            if model_type not in self.model_options:
                raise ValueError(f"Model type '{model_type}' tidak tersedia")
            
            self.model = self.model_options[model_type]
            self.model_type = model_type
            
            print(f"Training {model_type} model...")
            self.model.fit(X_train_scaled, y_train)
            
            # Make predictions
            y_pred = self.model.predict(X_test_scaled)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            # For regression, we'll use R² as "accuracy"
            accuracy = max(0, r2)  # Ensure non-negative
            
            self.metrics = {
                'accuracy': accuracy,
                'mse': mse,
                'r2_score': r2,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
            
            self.is_trained = True
            
            # Save model
            self._save_model()
            
            return {
                'success': True,
                'model_type': model_type,
                'metrics': self.metrics,
                'feature_names': self.feature_names
            }
            
        except Exception as e:
            print(f"Training error: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def predict(self, input_data):
        """Make prediction"""
        try:
            if not self.is_trained:
                # Try to load saved model
                if not self._load_model():
                    raise ValueError("Model belum ditraining")
            
            # Prepare input data
            if isinstance(input_data, dict):
                # Convert dict to array in correct order
                X = np.array([[input_data.get(feature, 0) for feature in self.feature_names]])
            else:
                X = np.array(input_data).reshape(1, -1)
            
            # Scale input
            X_scaled = self.scaler.transform(X)
            
            # Make prediction
            prediction = self.model.predict(X_scaled)[0]
            
            # Calculate confidence (simplified)
            confidence = min(1.0, max(0.1, abs(self.metrics.get('r2_score', 0.5))))
            
            return {
                'success': True,
                'prediction': float(prediction),
                'confidence': float(confidence)
            }
            
        except Exception as e:
            print(f"Prediction error: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_status(self):
        """Get model status"""
        if not self.is_trained:
            self._load_model()
        
        return {
            'trained': self.is_trained,
            'model_type': self.model_type,
            'feature_names': self.feature_names,
            'metrics': self.metrics if self.is_trained else None
        }

    def _save_model(self):
        """Save trained model to file"""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'model_type': self.model_type,
                'feature_names': self.feature_names,
                'metrics': self.metrics,
                'is_trained': self.is_trained
            }
            
            with open('trained_model.pkl', 'wb') as f:
                pickle.dump(model_data, f)
            
            print("Model saved successfully")
            
        except Exception as e:
            print(f"Error saving model: {e}")

    def _load_model(self):
        """Load trained model from file"""
        try:
            if os.path.exists('trained_model.pkl'):
                with open('trained_model.pkl', 'rb') as f:
                    model_data = pickle.load(f)
                
                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.model_type = model_data['model_type']
                self.feature_names = model_data['feature_names']
                self.metrics = model_data['metrics']
                self.is_trained = model_data['is_trained']
                
                print("Model loaded successfully")
                return True
            
            return False
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return False
