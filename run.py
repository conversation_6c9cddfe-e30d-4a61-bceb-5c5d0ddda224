#!/usr/bin/env python3
"""
Script untuk menjalankan sistem ML dengan setup otomatis
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """Check apakah dependencies sudah terinstall"""
    try:
        import numpy
        import pandas
        import sklearn
        print("✅ Dependencies sudah terinstall")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def install_dependencies():
    """Install dependencies dari requirements.txt"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies berhasil diinstall")
        return True
    except subprocess.CalledProcessError:
        print("❌ Gagal install dependencies")
        return False

def create_sample_data():
    """Create sample data untuk testing"""
    try:
        from data_handler import DataHandler
        
        handler = DataHandler()
        
        # Create regression dataset
        df_reg = handler.create_sample_csv('sample_regression.csv', 'regression')
        print(f"✅ Sample regression data created: {df_reg.shape}")
        
        # Create classification dataset  
        df_cls = handler.create_sample_csv('sample_classification.csv', 'classification')
        print(f"✅ Sample classification data created: {df_cls.shape}")
        
        return True
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Sistem ML Tanpa Framework")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n📦 Installing required dependencies...")
        if not install_dependencies():
            print("❌ Gagal install dependencies. Install manual dengan:")
            print("   pip install -r requirements.txt")
            return
    
    # Create sample data
    print("\n📊 Creating sample datasets...")
    create_sample_data()
    
    # Import dan jalankan server
    print("\n🌐 Starting server...")
    try:
        from server import run_server
        run_server(8000)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == '__main__':
    main()
