# 🚀 INSTRUKSI MENJALANKAN SISTEM ML TANPA FRAMEWORK

## 📋 Persyaratan Sistem

- **Windows 10/11** (sudah dioptimalkan untuk Windows)
- **Python 3.7+** (belum terinstall? lihat langkah 1)
- **Browser modern** (Chrome, Firefox, Edge)

## 🛠️ Langkah-langkah Instalasi

### 1. Install Python (jika belum ada)

1. **Download Python:**
   - Kunjungi: https://python.org/downloads/
   - Download versi terbaru untuk Windows

2. **Install Python:**
   - Jalankan installer
   - ⚠️ **PENTING**: Centang "Add Python to PATH"
   - Klik "Install Now"

3. **Verifikasi instalasi:**
   - Buka Command Prompt
   - Ketik: `python --version`
   - Harus muncul versi Python

### 2. Setup Sistem ML

**Opsi A: Otomatis (Recommended)**
```bash
# Double-click file ini:
install.bat
```

**Opsi B: Manual**
```bash
# Buka Command Prompt di folder project
python -m pip install -r requirements.txt
```

### 3. <PERSON>jalankan Sistem

**Opsi A: Otomatis**
```bash
# Double-click file ini:
start.bat
```

**Opsi B: Manual**
```bash
python server.py
```

### 4. Akses Web Interface

1. **Buka browser**
2. **Kunjungi:** http://localhost:8000
3. **Mulai gunakan sistem ML!**

## 🎯 Cara Menggunakan

### Tab 1: Training Model

1. **Pilih jenis model:**
   - Linear Regression (cepat, sederhana)
   - Random Forest (akurat, lambat)
   - SVM (untuk data kompleks)

2. **Upload data (opsional):**
   - Format CSV dengan header
   - Kolom terakhir = target/label
   - Atau gunakan dataset sample

3. **Set parameter:**
   - Test Size: 0.1-0.5 (default: 0.2)

4. **Klik "Mulai Training"**
   - Tunggu hingga selesai
   - Lihat hasil akurasi

### Tab 2: Prediksi

1. **Pastikan model sudah ditraining**
2. **Masukkan nilai untuk setiap feature**
3. **Klik "Prediksi"**
4. **Lihat hasil prediksi + confidence**

### Tab 3: Hasil

- **Metrics model:** Akurasi, MSE, R² Score
- **Info training:** Jumlah samples
- **Visualisasi** (akan ditambahkan)

## 📊 Format Data CSV

```csv
feature_1,feature_2,feature_3,target
1.2,3.4,5.6,10.5
2.1,4.3,6.5,12.3
3.0,2.1,7.8,15.2
```

**Aturan:**
- Header wajib ada
- Kolom terakhir = target
- Minimal 10 baris data
- Tidak boleh ada missing values berlebihan

## 🧪 Testing dengan Demo

```bash
# Jalankan server dulu
python server.py

# Di terminal lain:
python demo.py
```

Demo akan:
- Test koneksi server
- Create sample data
- Training model otomatis
- Test prediksi
- Tampilkan metrics

## 🔧 Troubleshooting

### ❌ "Python was not found"
**Solusi:**
1. Install Python dari python.org
2. Pastikan centang "Add to PATH"
3. Restart Command Prompt

### ❌ "pip is not recognized"
**Solusi:**
```bash
python -m ensurepip --upgrade
```

### ❌ "Port 8000 already in use"
**Solusi:**
```bash
# Gunakan port lain
python server.py 8080
# Akses: http://localhost:8080
```

### ❌ "Module not found"
**Solusi:**
```bash
# Install dependencies manual
pip install numpy pandas scikit-learn
```

### ❌ Server tidak bisa diakses
**Solusi:**
1. Cek firewall Windows
2. Pastikan server running (lihat console)
3. Coba browser lain
4. Restart server

### ❌ Training error
**Solusi:**
1. Cek format CSV
2. Pastikan ada minimal 10 samples
3. Hapus missing values berlebihan
4. Gunakan dataset sample dulu

## 📁 File-file Penting

```
ML/
├── 🌐 index.html       # Interface web utama
├── 🎨 style.css        # Styling modern
├── ⚡ script.js        # JavaScript interaktif
├── 🖥️  server.py       # HTTP server backend
├── 🤖 ml_model.py      # Model ML handler
├── 📊 data_handler.py  # Data processing
├── 📦 requirements.txt # Dependencies
├── 🚀 start.bat        # Quick start (Windows)
├── ⚙️  install.bat     # Setup otomatis
├── 🧪 demo.py          # Testing script
└── 📖 README.md        # Dokumentasi lengkap
```

## 🎨 Fitur UI

- **Design Modern:** Gradient background, cards, animations
- **Responsive:** Works di desktop & mobile
- **Real-time:** Progress indicators, status updates
- **Interactive:** Tabs, forms, hasil dinamis
- **No Framework:** Pure HTML/CSS/JS

## 🔬 Fitur ML

- **3 Model Types:** Linear, Random Forest, SVM
- **Auto Scaling:** StandardScaler untuk features
- **Model Persistence:** Save/load dengan pickle
- **Metrics:** Accuracy, MSE, R² Score
- **Sample Data:** Auto-generate jika tidak upload

## 🌟 Keunggulan

✅ **Tanpa Framework Web** - Pure HTML/CSS/JS
✅ **Backend Sederhana** - Built-in Python HTTP server
✅ **ML Powerful** - Scikit-learn models
✅ **Easy Setup** - One-click installation
✅ **Cross-platform** - Works di Windows/Mac/Linux
✅ **Educational** - Source code mudah dipahami

## 📞 Support

Jika ada masalah:
1. Cek troubleshooting di atas
2. Lihat console error di browser (F12)
3. Cek terminal output server
4. Pastikan semua file ada

## 🎯 Next Steps

Setelah sistem berjalan:
1. Coba upload data sendiri
2. Experiment dengan model types
3. Modifikasi UI sesuai kebutuhan
4. Tambah model ML baru
5. Implementasi visualisasi data

**Selamat menggunakan Sistem ML Tanpa Framework! 🚀**
