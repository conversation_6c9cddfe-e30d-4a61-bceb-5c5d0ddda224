@echo off
echo ========================================
echo   Setup ML System - Agil's Python
echo ========================================
echo.

REM Python yang ditemukan di sistem Agil
set "PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo 🐍 Using Python: %PYTHON_EXE%
echo.

REM Test Python
echo 🧪 Testing Python...
"%PYTHON_EXE%" --version
if %errorlevel% neq 0 (
    echo ❌ Python tidak bisa dijalankan
    echo 💡 Coba lokasi alternatif...
    
    REM Try alternative locations
    set "PYTHON_EXE=C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe"
    echo Trying: %PYTHON_EXE%
    "%PYTHON_EXE%" --version
    if %errorlevel% neq 0 (
        echo ❌ Semua lokasi Python gagal
        pause
        exit /b 1
    )
)

echo ✅ Python working!
echo.

echo 📦 Upgrading pip...
"%PYTHON_EXE%" -m pip install --upgrade pip

echo.
echo 📦 Installing ML dependencies...
"%PYTHON_EXE%" -m pip install numpy pandas scikit-learn

if %errorlevel% neq 0 (
    echo ❌ Gagal install dependencies
    echo.
    echo 💡 Trying with --user flag...
    "%PYTHON_EXE%" -m pip install --user numpy pandas scikit-learn
    
    if %errorlevel% neq 0 (
        echo ❌ Masih gagal. Coba manual:
        echo "%PYTHON_EXE%" -m pip install --user numpy
        echo "%PYTHON_EXE%" -m pip install --user pandas  
        echo "%PYTHON_EXE%" -m pip install --user scikit-learn
        pause
        exit /b 1
    )
)

echo.
echo ✅ Setup berhasil!
echo.

REM Create run script
echo @echo off > run_ml_agil.bat
echo echo ======================================== >> run_ml_agil.bat
echo echo   ML System - Starting Server >> run_ml_agil.bat
echo echo ======================================== >> run_ml_agil.bat
echo echo. >> run_ml_agil.bat
echo echo 🚀 Starting server with Python: >> run_ml_agil.bat
echo echo    %PYTHON_EXE% >> run_ml_agil.bat
echo echo. >> run_ml_agil.bat
echo echo 🌐 Server akan berjalan di: http://localhost:8000 >> run_ml_agil.bat
echo echo 🔗 Buka URL tersebut di browser >> run_ml_agil.bat
echo echo ⏹️  Tekan Ctrl+C untuk stop server >> run_ml_agil.bat
echo echo. >> run_ml_agil.bat
echo "%PYTHON_EXE%" server.py >> run_ml_agil.bat
echo pause >> run_ml_agil.bat

echo 📝 File 'run_ml_agil.bat' telah dibuat
echo.
echo 🚀 Untuk menjalankan sistem ML:
echo    Double-click: run_ml_agil.bat
echo.
echo 🌐 Kemudian buka browser ke:
echo    http://localhost:8000
echo.
echo ✅ Setup complete!
pause
