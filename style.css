/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Tabs */
.tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 10px;
}

.tab-button {
    padding: 12px 24px;
    border: none;
    background: rgba(255,255,255,0.2);
    color: white;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.tab-button:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.tab-button.active {
    background: white;
    color: #667eea;
    font-weight: bold;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Cards */
.card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.card h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
    display: block;
}

/* Buttons */
.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Status dan Result Boxes */
.status-box,
.result-box {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin-top: 20px;
    border-radius: 8px;
}

.status-box h3,
.result-box h3 {
    color: #667eea;
    margin-bottom: 15px;
}

/* Metrics Container */
.metrics-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    border: 2px solid #e9ecef;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.metric-label {
    color: #666;
    font-size: 0.9rem;
}

/* Visualization Container */
.viz-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer */
footer {
    text-align: center;
    margin-top: 50px;
    color: white;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-button {
        width: 200px;
        margin-bottom: 10px;
    }
    
    .card {
        padding: 20px;
    }
}
