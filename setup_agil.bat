@echo off
echo ========================================
echo   Setup Khusus untuk Agil
echo   Python Location: Custom Path
echo ========================================
echo.

REM Set path Python yang spesifik untuk Agil
set "PYTHON_PATH=C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13"

echo 🔍 Mencari Python di lokasi Anda...
echo 📍 Path: %PYTHON_PATH%
echo.

REM Cek berbagai kemungkinan nama file Python
if exist "%PYTHON_PATH%\python.exe" (
    set "PYTHON_EXE=%PYTHON_PATH%\python.exe"
    echo ✅ Ditemukan: python.exe
    goto :found
)

if exist "%PYTHON_PATH%\Python.exe" (
    set "PYTHON_EXE=%PYTHON_PATH%\Python.exe"
    echo ✅ Ditemukan: Python.exe
    goto :found
)

if exist "%PYTHON_PATH%\python3.exe" (
    set "PYTHON_EXE=%PYTHON_PATH%\python3.exe"
    echo ✅ Ditemukan: python3.exe
    goto :found
)

REM Cek di subfolder
if exist "%PYTHON_PATH%\Scripts\python.exe" (
    set "PYTHON_EXE=%PYTHON_PATH%\Scripts\python.exe"
    echo ✅ Ditemukan: Scripts\python.exe
    goto :found
)

REM List isi folder untuk debugging
echo 📂 Isi folder Python:
dir "%PYTHON_PATH%" /b 2>nul
echo.

echo ❌ Python executable tidak ditemukan di lokasi tersebut
echo.
echo 💡 Solusi:
echo 1. Cek apakah path benar: %PYTHON_PATH%
echo 2. Atau cari file python.exe di folder tersebut
echo 3. Atau install Python dari python.org dengan "Add to PATH"
echo.
pause
exit /b 1

:found
echo.
echo 🧪 Testing Python...
"%PYTHON_EXE%" --version
if %errorlevel% neq 0 (
    echo ❌ Python tidak bisa dijalankan
    pause
    exit /b 1
)

echo.
echo 📦 Installing dependencies...
"%PYTHON_EXE%" -m pip install --upgrade pip
"%PYTHON_EXE%" -m pip install numpy pandas scikit-learn

if %errorlevel% neq 0 (
    echo ❌ Gagal install dependencies
    echo.
    echo 💡 Coba manual:
    echo "%PYTHON_EXE%" -m pip install --user numpy pandas scikit-learn
    pause
    exit /b 1
)

echo.
echo ✅ Setup berhasil!
echo.
echo 🚀 Untuk menjalankan sistem:
echo    "%PYTHON_EXE%" server.py
echo.
echo 🌐 Kemudian buka browser ke:
echo    http://localhost:8000
echo.

REM Buat shortcut untuk menjalankan
echo @echo off > run_ml_system.bat
echo echo Starting ML System... >> run_ml_system.bat
echo "%PYTHON_EXE%" server.py >> run_ml_system.bat
echo pause >> run_ml_system.bat

echo 📝 File 'run_ml_system.bat' telah dibuat untuk menjalankan sistem
echo.
pause
