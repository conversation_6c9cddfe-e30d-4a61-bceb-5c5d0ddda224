# 🚀 INSTRUKSI KHUSUS UNTUK AGIL

## 📍 Situasi Anda
Python terinstall di lokasi custom: 
```
C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13
```

## ⚡ QUICK START (Recommended)

### Opsi 1: Auto-Finder (Paling Mudah)
```bash
# Double-click file ini untuk mencari Python otomatis:
find_python.bat
```
Script ini akan:
- Mencari Python di seluruh sistem
- Membuat script custom untuk Anda
- Setup dependencies otomatis

### Opsi 2: Setup Manual Khusus
```bash
# Double-click file ini:
setup_agil.bat
```

## 🛠️ Langkah Detail

### 1. Cari Python di Sistem Anda

**Jalankan:** `find_python.bat`

Script akan mencari Python di:
- PATH environment
- Lokasi Anda: `C:\Users\<USER>\AppData\Roaming\...`
- Program Files
- AppData locations
- Seluruh drive C: (jika perlu)

### 2. Install Dependencies

Setelah Python ditemukan, akan dibuat file:
- `install_custom.bat` - untuk install dependencies
- `run_custom.bat` - untuk menjalankan sistem

**Jalankan:** `install_custom.bat`

### 3. Menjalankan Sistem

**Jalankan:** `run_custom.bat`

Atau manual:
```bash
"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe" server.py
```

### 4. Akses Web Interface

Buka browser ke: **http://localhost:8000**

## 🔧 Troubleshooting Khusus

### ❌ "Python tidak ditemukan"

**Cek lokasi file:**
1. Buka File Explorer
2. Navigate ke: `C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13`
3. Cari file `python.exe` atau `Python.exe`

**Kemungkinan nama file:**
- `python.exe`
- `Python.exe` 
- `python3.exe`
- `python313.exe`

### ❌ "Folder tidak ada"

**Cari folder Python:**
1. Tekan `Win + R`
2. Ketik: `%APPDATA%`
3. Navigate ke folder Python
4. Atau search "python.exe" di File Explorer

### ❌ "pip tidak ditemukan"

**Install pip manual:**
```bash
"path\to\python.exe" -m ensurepip --upgrade
"path\to\python.exe" -m pip install --upgrade pip
```

### ❌ "Permission denied"

**Run as Administrator:**
1. Right-click pada `.bat` file
2. Pilih "Run as administrator"

## 📝 Manual Commands

Jika script otomatis tidak work, gunakan command manual:

### Install Dependencies:
```bash
"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe" -m pip install numpy pandas scikit-learn
```

### Run Server:
```bash
"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe" server.py
```

### Check Python Version:
```bash
"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe" --version
```

## 🎯 Alternative Solutions

### Opsi A: Add Python to PATH
1. Copy path: `C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13`
2. Tekan `Win + X` → System
3. Advanced system settings
4. Environment Variables
5. Edit PATH, tambahkan path Python
6. Restart Command Prompt

### Opsi B: Install Python Baru
1. Download dari python.org
2. Install dengan "Add to PATH" checked
3. Gunakan script `install.bat` dan `start.bat` yang original

### Opsi C: Use Python Launcher
```bash
py --version
py -m pip install numpy pandas scikit-learn
py server.py
```

## 📊 Expected Output

Setelah berhasil, Anda akan melihat:

```
🚀 Server berjalan di http://localhost:8000
📊 Sistem ML siap digunakan!
🔗 Buka http://localhost:8000 di browser
⏹️  Tekan Ctrl+C untuk menghentikan server
```

## 🌟 Tips

1. **Bookmark lokasi Python** untuk referensi future
2. **Buat shortcut** ke `run_custom.bat` di desktop
3. **Test dengan demo.py** setelah server running
4. **Gunakan dataset sample** untuk testing pertama

## 📞 Jika Masih Bermasalah

1. **Screenshot error message** yang muncul
2. **Cek isi folder Python** Anda
3. **Coba run command manual** di Command Prompt
4. **Pastikan Python 3.7+** (Anda punya 3.13, jadi OK)

**Selamat mencoba! 🚀**
