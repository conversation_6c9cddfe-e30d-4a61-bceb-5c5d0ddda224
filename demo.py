#!/usr/bin/env python3
"""
Demo script untuk menguji sistem ML tanpa framework
"""

import requests
import json
import time
import pandas as pd
from data_handler import DataHandler

def test_server_connection():
    """Test koneksi ke server"""
    try:
        response = requests.get('http://localhost:8000/model/status')
        print("✅ Server connection OK")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server tidak berjalan. Jalankan 'python server.py' terlebih dahulu")
        return False

def create_demo_data():
    """Create demo data CSV"""
    handler = DataHandler()
    df = handler.generate_sample_dataset('regression', 100)
    df.to_csv('demo_data.csv', index=False)
    print(f"✅ Demo data created: {df.shape}")
    return df

def demo_training():
    """Demo training model"""
    print("\n🚀 Demo Training Model...")
    
    # Prepare training data
    data = {
        'model_type': 'random_forest',
        'test_size': 0.2
    }
    
    try:
        response = requests.post('http://localhost:8000/train', data=data)
        result = response.json()
        
        if result.get('success'):
            print("✅ Training berhasil!")
            print(f"   Model: {result['model_type']}")
            print(f"   Akurasi: {result['metrics']['accuracy']:.4f}")
            print(f"   MSE: {result['metrics']['mse']:.4f}")
            print(f"   R² Score: {result['metrics']['r2_score']:.4f}")
            return True
        else:
            print(f"❌ Training gagal: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_prediction():
    """Demo prediction"""
    print("\n🔮 Demo Prediction...")
    
    # Sample input data
    input_data = {
        'feature_1': 1.5,
        'feature_2': -0.5,
        'feature_3': 2.0,
        'feature_4': 0.8,
        'feature_5': -1.2
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/predict',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(input_data)
        )
        
        result = response.json()
        
        if result.get('success'):
            print("✅ Prediksi berhasil!")
            print(f"   Input: {input_data}")
            print(f"   Prediksi: {result['prediction']:.4f}")
            print(f"   Confidence: {result['confidence']:.2f}")
            return True
        else:
            print(f"❌ Prediksi gagal: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_model_status():
    """Demo check model status"""
    print("\n📊 Demo Model Status...")
    
    try:
        response = requests.get('http://localhost:8000/model/status')
        result = response.json()
        
        print(f"   Trained: {result.get('trained')}")
        print(f"   Model Type: {result.get('model_type')}")
        print(f"   Features: {result.get('feature_names')}")
        
        if result.get('metrics'):
            metrics = result['metrics']
            print(f"   Metrics:")
            print(f"     - Akurasi: {metrics.get('accuracy', 0):.4f}")
            print(f"     - MSE: {metrics.get('mse', 0):.4f}")
            print(f"     - R² Score: {metrics.get('r2_score', 0):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main demo function"""
    print("🎯 Demo Sistem ML Tanpa Framework")
    print("=" * 50)
    
    # Test server connection
    if not test_server_connection():
        return
    
    # Create demo data
    create_demo_data()
    
    # Demo training
    if demo_training():
        time.sleep(1)  # Wait a bit
        
        # Demo prediction
        demo_prediction()
        time.sleep(1)
        
        # Demo status
        demo_model_status()
    
    print("\n✅ Demo selesai!")
    print("\n🌐 Buka http://localhost:8000 di browser untuk interface web")

if __name__ == '__main__':
    main()
