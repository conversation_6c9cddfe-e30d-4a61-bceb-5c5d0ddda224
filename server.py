#!/usr/bin/env python3
"""
HTTP Server sederhana untuk sistem ML tanpa framework
Menggunakan built-in http.server Python
"""

import json
import os
import sys
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import cgi
import io
from ml_model import MLModel
from data_handler import DataHandler

class MLHTTPRequestHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.ml_model = MLModel()
        self.data_handler = DataHandler()
        super().__init__(*args, **kwargs)

    def _set_cors_headers(self):
        """Set CORS headers untuk mengizinkan request dari frontend"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def _send_json_response(self, data, status_code=200):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self._set_cors_headers()
        self.end_headers()
        
        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))

    def _send_file_response(self, file_path, content_type='text/html'):
        """Send file response"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', content_type)
            self._set_cors_headers()
            self.end_headers()
            self.wfile.write(content)
            
        except FileNotFoundError:
            self.send_response(404)
            self.send_header('Content-Type', 'text/html')
            self._set_cors_headers()
            self.end_headers()
            self.wfile.write(b'<h1>404 - File Not Found</h1>')

    def do_OPTIONS(self):
        """Handle preflight requests"""
        self.send_response(200)
        self._set_cors_headers()
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Serve static files
        if path == '/' or path == '/index.html':
            self._send_file_response('index.html')
        elif path == '/style.css':
            self._send_file_response('style.css', 'text/css')
        elif path == '/script.js':
            self._send_file_response('script.js', 'application/javascript')
        elif path == '/model/status':
            self._handle_model_status()
        else:
            self.send_response(404)
            self._set_cors_headers()
            self.end_headers()
            self.wfile.write(b'<h1>404 - Not Found</h1>')

    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/train':
            self._handle_train()
        elif path == '/predict':
            self._handle_predict()
        else:
            self._send_json_response({'error': 'Endpoint not found'}, 404)

    def _handle_model_status(self):
        """Handle model status request"""
        try:
            status = self.ml_model.get_status()
            self._send_json_response(status)
        except Exception as e:
            self._send_json_response({'error': str(e)}, 500)

    def _handle_train(self):
        """Handle training request"""
        try:
            # Parse multipart form data
            content_type = self.headers.get('Content-Type', '')
            
            if 'multipart/form-data' in content_type:
                form = cgi.FieldStorage(
                    fp=self.rfile,
                    headers=self.headers,
                    environ={'REQUEST_METHOD': 'POST'}
                )
                
                model_type = form.getvalue('model_type', 'linear_regression')
                test_size = float(form.getvalue('test_size', 0.2))
                
                # Handle file upload
                data_file = None
                if 'data_file' in form:
                    file_item = form['data_file']
                    if file_item.filename:
                        data_file = io.StringIO(file_item.file.read().decode('utf-8'))
                
                # Train model
                start_time = time.time()
                result = self.ml_model.train(
                    model_type=model_type,
                    test_size=test_size,
                    data_file=data_file
                )
                training_time = time.time() - start_time
                
                result['training_time'] = training_time
                self._send_json_response(result)
                
            else:
                self._send_json_response({'error': 'Invalid content type'}, 400)
                
        except Exception as e:
            print(f"Training error: {e}")
            self._send_json_response({'error': str(e)}, 500)

    def _handle_predict(self):
        """Handle prediction request"""
        try:
            # Read JSON data
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # Make prediction
            result = self.ml_model.predict(data)
            self._send_json_response(result)
            
        except Exception as e:
            print(f"Prediction error: {e}")
            self._send_json_response({'error': str(e)}, 500)

    def log_message(self, format, *args):
        """Custom log message"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server(port=8000):
    """Run the HTTP server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MLHTTPRequestHandler)
    
    print(f"🚀 Server berjalan di http://localhost:{port}")
    print("📊 Sistem ML siap digunakan!")
    print("🔗 Buka http://localhost:8000 di browser")
    print("⏹️  Tekan Ctrl+C untuk menghentikan server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server dihentikan")
        httpd.server_close()

if __name__ == '__main__':
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Port harus berupa angka")
            sys.exit(1)
    
    run_server(port)
