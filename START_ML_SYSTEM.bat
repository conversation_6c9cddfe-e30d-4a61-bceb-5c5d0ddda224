@echo off
echo ========================================
echo   🚀 SISTEM ML TANPA FRAMEWORK
echo   Ready to use - Agil's Version
echo ========================================
echo.

REM Python yang sudah ditest dan working
set "PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo 🐍 Python: %PYTHON_EXE%
echo 📦 Dependencies: ✅ Already installed
echo 🌐 Server: Starting...
echo.

echo ⚡ QUICK ACCESS:
echo    🔗 Web Interface: http://localhost:8000
echo    📊 ML System: Ready to use
echo    ⏹️  Stop Server: Ctrl+C
echo.

echo ========================================
echo   Server Output:
echo ========================================

REM Start the ML system
"%PYTHON_EXE%" server.py

echo.
echo 🛑 Server stopped
pause
