@echo off
echo ========================================
echo   Python Finder - Mencari Python di PC
echo ========================================
echo.

echo 🔍 Mencari Python di berbagai lokasi...
echo.

set "FOUND_PYTHON="

REM Check PATH first
echo [1] Checking PATH...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python ditemukan di PATH
    python --version
    set "FOUND_PYTHON=python"
    goto :found
) else (
    echo ❌ Python tidak ada di PATH
)

echo.
echo [2] Checking lokasi Agil...
set "AGIL_PATH=C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13"
if exist "%AGIL_PATH%\python.exe" (
    echo ✅ Python ditemukan di: %AGIL_PATH%
    "%AGIL_PATH%\python.exe" --version
    set "FOUND_PYTHON=%AGIL_PATH%\python.exe"
    goto :found
) else (
    echo ❌ Tidak ada di: %AGIL_PATH%
)

echo.
echo [3] Checking common locations...

REM Common Python locations
for %%L in (
    "C:\Python*"
    "C:\Program Files\Python*"
    "C:\Program Files (x86)\Python*"
    "%LOCALAPPDATA%\Programs\Python\Python*"
    "%APPDATA%\Local\Programs\Python\Python*"
    "%USERPROFILE%\AppData\Local\Programs\Python\Python*"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python*"
) do (
    echo Checking: %%L
    for /d %%D in ("%%L") do (
        if exist "%%D\python.exe" (
            echo ✅ Found: %%D\python.exe
            "%%D\python.exe" --version 2>nul
            if !errorlevel! equ 0 (
                set "FOUND_PYTHON=%%D\python.exe"
                goto :found
            )
        )
    )
)

echo.
echo [4] Searching entire C: drive (this may take a while)...
echo 🔍 Searching for python.exe...

for /f "delims=" %%F in ('dir C:\python.exe /s /b 2^>nul') do (
    echo Found: %%F
    "%%F" --version >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Working Python: %%F
        set "FOUND_PYTHON=%%F"
        goto :found
    )
)

echo.
echo ❌ Python tidak ditemukan di sistem ini!
echo.
echo 📥 Silakan install Python:
echo 1. Download dari: https://python.org/downloads/
echo 2. Pilih "Add Python to PATH" saat install
echo 3. Restart command prompt setelah install
echo.
goto :end

:found
echo.
echo ========================================
echo ✅ PYTHON DITEMUKAN!
echo ========================================
echo.
echo 📍 Lokasi: %FOUND_PYTHON%
echo 🐍 Versi: 
"%FOUND_PYTHON%" --version
echo.

echo 📝 Membuat script khusus untuk Anda...

REM Create custom install script
echo @echo off > install_custom.bat
echo set "PYTHON_EXE=%FOUND_PYTHON%" >> install_custom.bat
echo echo Installing dependencies with: %%PYTHON_EXE%% >> install_custom.bat
echo "%%PYTHON_EXE%%" -m pip install --upgrade pip >> install_custom.bat
echo "%%PYTHON_EXE%%" -m pip install numpy pandas scikit-learn >> install_custom.bat
echo echo Setup complete! >> install_custom.bat
echo pause >> install_custom.bat

REM Create custom run script
echo @echo off > run_custom.bat
echo set "PYTHON_EXE=%FOUND_PYTHON%" >> run_custom.bat
echo echo Starting ML System... >> run_custom.bat
echo echo Python: %%PYTHON_EXE%% >> run_custom.bat
echo "%%PYTHON_EXE%%" server.py >> run_custom.bat
echo pause >> run_custom.bat

echo ✅ File dibuat:
echo    - install_custom.bat (untuk install dependencies)
echo    - run_custom.bat (untuk menjalankan sistem)
echo.
echo 🚀 Langkah selanjutnya:
echo 1. Double-click install_custom.bat
echo 2. Double-click run_custom.bat
echo 3. Buka http://localhost:8000 di browser
echo.

:end
pause
