"""
Data Handler untuk preprocessing dan manipulasi data
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
import io

class DataHandler:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
    def load_csv(self, file_path_or_buffer):
        """Load CSV file"""
        try:
            if isinstance(file_path_or_buffer, str):
                df = pd.read_csv(file_path_or_buffer)
            else:
                df = pd.read_csv(file_path_or_buffer)
            
            return df
        except Exception as e:
            raise ValueError(f"Error loading CSV: {e}")
    
    def preprocess_data(self, df, target_column=None):
        """Preprocess data untuk training"""
        try:
            # Make a copy
            processed_df = df.copy()
            
            # Handle missing values
            processed_df = self._handle_missing_values(processed_df)
            
            # Encode categorical variables
            processed_df = self._encode_categorical(processed_df)
            
            # Separate features and target
            if target_column:
                if target_column not in processed_df.columns:
                    raise ValueError(f"Target column '{target_column}' not found")
                
                X = processed_df.drop(columns=[target_column])
                y = processed_df[target_column]
            else:
                # Assume last column is target
                X = processed_df.iloc[:, :-1]
                y = processed_df.iloc[:, -1]
            
            return X, y
            
        except Exception as e:
            raise ValueError(f"Error preprocessing data: {e}")
    
    def _handle_missing_values(self, df):
        """Handle missing values"""
        # For numeric columns, fill with mean
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df[col].fillna(df[col].mean(), inplace=True)
        
        # For categorical columns, fill with mode
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            df[col].fillna(df[col].mode()[0] if not df[col].mode().empty else 'Unknown', inplace=True)
        
        return df
    
    def _encode_categorical(self, df):
        """Encode categorical variables"""
        categorical_columns = df.select_dtypes(include=['object']).columns
        
        for col in categorical_columns:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
            
            df[col] = self.label_encoders[col].fit_transform(df[col].astype(str))
        
        return df
    
    def scale_features(self, X_train, X_test=None):
        """Scale features using StandardScaler"""
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        if X_test is not None:
            X_test_scaled = self.scaler.transform(X_test)
            return X_train_scaled, X_test_scaled
        
        return X_train_scaled
    
    def get_data_info(self, df):
        """Get information about the dataset"""
        info = {
            'shape': df.shape,
            'columns': df.columns.tolist(),
            'dtypes': df.dtypes.to_dict(),
            'missing_values': df.isnull().sum().to_dict(),
            'numeric_columns': df.select_dtypes(include=[np.number]).columns.tolist(),
            'categorical_columns': df.select_dtypes(include=['object']).columns.tolist()
        }
        
        return info
    
    def generate_sample_dataset(self, dataset_type='regression', n_samples=1000):
        """Generate sample dataset untuk testing"""
        np.random.seed(42)
        
        if dataset_type == 'regression':
            # Generate regression dataset
            n_features = 5
            X = np.random.randn(n_samples, n_features)
            
            # Create target with some relationship to features
            y = (X[:, 0] * 2 + 
                 X[:, 1] * -1.5 + 
                 X[:, 2] * 0.5 + 
                 X[:, 3] * 1.2 + 
                 X[:, 4] * -0.8 + 
                 np.random.randn(n_samples) * 0.1)
            
            # Create DataFrame
            feature_names = [f'feature_{i+1}' for i in range(n_features)]
            df = pd.DataFrame(X, columns=feature_names)
            df['target'] = y
            
        elif dataset_type == 'classification':
            # Generate classification dataset
            n_features = 4
            X = np.random.randn(n_samples, n_features)
            
            # Create binary target
            y = (X[:, 0] + X[:, 1] - X[:, 2] + X[:, 3] > 0).astype(int)
            
            # Create DataFrame
            feature_names = [f'feature_{i+1}' for i in range(n_features)]
            df = pd.DataFrame(X, columns=feature_names)
            df['target'] = y
            
        else:
            raise ValueError("dataset_type must be 'regression' or 'classification'")
        
        return df
    
    def validate_data(self, df):
        """Validate dataset"""
        issues = []
        
        # Check if empty
        if df.empty:
            issues.append("Dataset is empty")
        
        # Check for all missing values in any column
        for col in df.columns:
            if df[col].isnull().all():
                issues.append(f"Column '{col}' has all missing values")
        
        # Check for single unique value columns
        for col in df.columns:
            if df[col].nunique() <= 1:
                issues.append(f"Column '{col}' has only one unique value")
        
        # Check minimum number of samples
        if len(df) < 10:
            issues.append("Dataset has too few samples (minimum 10 required)")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues
        }
    
    def create_sample_csv(self, filename='sample_data.csv', dataset_type='regression'):
        """Create sample CSV file"""
        df = self.generate_sample_dataset(dataset_type)
        df.to_csv(filename, index=False)
        print(f"Sample {dataset_type} dataset saved as {filename}")
        return df
