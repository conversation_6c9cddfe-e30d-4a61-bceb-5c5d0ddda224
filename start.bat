@echo off
echo ========================================
echo   Sistem ML Tanpa Framework
echo ========================================
echo.

REM Set Python path untuk lokasi custom
set "PYTHON_CUSTOM_PATH=C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13"
set "PYTHON_EXE="

REM Check if Python is in PATH
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set "PYTHON_EXE=python"
    goto :python_found
)

REM Check if Python is in custom location
if exist "%PYTHON_CUSTOM_PATH%\python.exe" (
    set "PYTHON_EXE=%PYTHON_CUSTOM_PATH%\python.exe"
    goto :python_found
)

REM Try common Python locations
for %%p in (
    "C:\Python*\python.exe"
    "C:\Program Files\Python*\python.exe"
    "C:\Program Files (x86)\Python*\python.exe"
    "%LOCALAPPDATA%\Programs\Python\Python*\python.exe"
    "%APPDATA%\Local\Programs\Python\Python*\python.exe"
) do (
    if exist "%%p" (
        set "PYTHON_EXE=%%p"
        goto :python_found
    )
)

echo ❌ Python tidak ditemukan!
echo    Jalankan install.bat terlebih dahulu
pause
exit /b 1

:python_found
echo ✅ Python ditemukan: %PYTHON_EXE%
echo 🚀 Starting ML System Server...
echo.
echo 🌐 Server akan berjalan di: http://localhost:8000
echo 🔗 Buka URL tersebut di browser Anda
echo.
echo ⏹️  Tekan Ctrl+C untuk menghentikan server
echo.

"%PYTHON_EXE%" server.py

pause
