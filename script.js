// Global variables
let currentModel = null;
let modelMetrics = null;
let isModelTrained = false;

// API Base URL
const API_BASE = 'http://localhost:8000';

// Tab functionality
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));
    
    // Remove active class from all buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab
    document.getElementById(tabName).classList.add('active');
    event.target.classList.add('active');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    checkModelStatus();
});

function setupEventListeners() {
    // Training form
    document.getElementById('trainingForm').addEventListener('submit', handleTraining);
    
    // Prediction form
    document.getElementById('predictionForm').addEventListener('submit', handlePrediction);
}

// Check if model is already trained
async function checkModelStatus() {
    try {
        const response = await fetch(`${API_BASE}/model/status`);
        const data = await response.json();
        
        if (data.trained) {
            isModelTrained = true;
            currentModel = data.model_type;
            modelMetrics = data.metrics;
            updateModelMetrics(data.metrics);
            generateInputFields(data.feature_names);
        }
    } catch (error) {
        console.log('Model belum ditraining atau server belum berjalan');
    }
}

// Handle training
async function handleTraining(event) {
    event.preventDefault();

    const modelType = document.getElementById('modelType').value;
    const testSize = document.getElementById('testSize').value;
    const dataFile = document.getElementById('dataFile').files[0];

    // Show training status
    const statusBox = document.getElementById('trainingStatus');
    const progressDiv = document.getElementById('trainingProgress');
    statusBox.style.display = 'block';
    progressDiv.innerHTML = '<div class="loading"></div> Memulai training model...';

    try {
        let response;

        if (dataFile) {
            // Use FormData for file upload
            const formData = new FormData();
            formData.append('model_type', modelType);
            formData.append('test_size', testSize);
            formData.append('data_file', dataFile);

            response = await fetch(`${API_BASE}/train`, {
                method: 'POST',
                body: formData
            });
        } else {
            // Use JSON for simple training without file
            const jsonData = {
                model_type: modelType,
                test_size: parseFloat(testSize)
            };

            response = await fetch(`${API_BASE}/train`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(jsonData)
            });
        }

        const data = await response.json();
        
        if (data.success) {
            progressDiv.innerHTML = `
                <div style="color: green;">
                    ✅ Training berhasil!<br>
                    Model: ${data.model_type}<br>
                    Akurasi: ${(data.metrics.accuracy * 100).toFixed(2)}%<br>
                    Waktu training: ${data.training_time.toFixed(2)} detik
                </div>
            `;
            
            // Update global state
            isModelTrained = true;
            currentModel = data.model_type;
            modelMetrics = data.metrics;
            
            // Update UI
            updateModelMetrics(data.metrics);
            generateInputFields(data.feature_names);
            
        } else {
            progressDiv.innerHTML = `<div style="color: red;">❌ Error: ${data.error}</div>`;
        }
        
    } catch (error) {
        progressDiv.innerHTML = `<div style="color: red;">❌ Error: ${error.message}</div>`;
    }
}

// Handle prediction
async function handlePrediction(event) {
    event.preventDefault();
    
    if (!isModelTrained) {
        alert('Model belum ditraining! Silakan training model terlebih dahulu.');
        return;
    }
    
    // Collect input values
    const inputs = {};
    const inputFields = document.querySelectorAll('#inputFields input');
    
    inputFields.forEach(input => {
        inputs[input.name] = parseFloat(input.value) || 0;
    });
    
    try {
        const response = await fetch(`${API_BASE}/predict`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(inputs)
        });
        
        const data = await response.json();
        
        if (data.success) {
            const resultBox = document.getElementById('predictionResult');
            const outputDiv = document.getElementById('predictionOutput');
            
            outputDiv.innerHTML = `
                <div class="prediction-result">
                    <h4>Hasil Prediksi:</h4>
                    <div class="prediction-value">${data.prediction.toFixed(4)}</div>
                    <small>Confidence: ${(data.confidence * 100).toFixed(1)}%</small>
                </div>
            `;
            
            resultBox.style.display = 'block';
            
        } else {
            alert(`Error: ${data.error}`);
        }
        
    } catch (error) {
        alert(`Error: ${error.message}`);
    }
}

// Update model metrics display
function updateModelMetrics(metrics) {
    const metricsContainer = document.getElementById('modelMetrics');
    
    metricsContainer.innerHTML = `
        <div class="metric-card">
            <div class="metric-value">${(metrics.accuracy * 100).toFixed(2)}%</div>
            <div class="metric-label">Akurasi</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${metrics.mse.toFixed(4)}</div>
            <div class="metric-label">Mean Squared Error</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${metrics.r2_score.toFixed(4)}</div>
            <div class="metric-label">R² Score</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${metrics.training_samples}</div>
            <div class="metric-label">Training Samples</div>
        </div>
    `;
}

// Generate input fields for prediction
function generateInputFields(featureNames) {
    const inputFieldsContainer = document.getElementById('inputFields');
    
    let fieldsHTML = '';
    featureNames.forEach(feature => {
        fieldsHTML += `
            <div class="form-group">
                <label for="${feature}">${feature}:</label>
                <input type="number" id="${feature}" name="${feature}" step="any" required>
            </div>
        `;
    });
    
    inputFieldsContainer.innerHTML = fieldsHTML;
}

// Utility functions
function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="loading"></div> Loading...';
}

function hideLoading(elementId) {
    document.getElementById(elementId).innerHTML = '';
}

// Add some CSS for prediction result
const style = document.createElement('style');
style.textContent = `
    .prediction-result {
        text-align: center;
        padding: 20px;
    }
    
    .prediction-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        margin: 15px 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border: 2px solid #e9ecef;
    }
`;
document.head.appendChild(style);
